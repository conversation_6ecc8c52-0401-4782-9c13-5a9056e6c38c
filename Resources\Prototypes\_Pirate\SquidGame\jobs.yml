# ГРАВЕЦЬ SQUID GAME
- type: job
  id: SquidGamePlayer
  name: job-name-squidgame-player
  description: job-description-squidgame-player
  playTimeTracker: JobSquidGamePlayer
  startingGear: SquidGamePlayer
  icon: "JobIconSquidGamePlayer"
  supervisors: job-supervisors-squidgame-guards
  canBeAntag: false
  categories: [ HideSpawnMenu ]
  access:
  - Maintenance
  requirements:
    - !type:CharacterSpeciesRequirement
      species:
      - Human
  special:
  - !type:AddComponentSpecial
    components:
    - type: MindShield
    - type: AntagImmune

# ОХОРОНЕЦЬ КОЛО
- type: job
  id: SquidGameGuardCircle
  name: job-name-squidgame-guard-circle
  description: job-description-squidgame-guard-circle
  playTimeTracker: JobSquidGameGuardCircle
  startingGear: SquidGameCircle
  icon: "JobIconSquidGameGuardCircle"
  supervisors: job-supervisors-squidgame-triangle
  canBeAntag: false
  categories: [ HideSpawnMenu ]
  access:
  - Security
  - Maintenance
  requirements:
    - !type:CharacterSpeciesRequirement
      species:
      - Human
    - !type:CharacterOverallTimeRequirement
      min: 18000 # 5 hours
  special:
  - !type:AddComponentSpecial
    components:
    - type: MindShield
    - type: AntagImmune

# ОХОРОНЕЦЬ ТРИКУТНИК
- type: job
  id: SquidGameGuardTriangle
  name: job-name-squidgame-guard-triangle
  description: job-description-squidgame-guard-triangle
  playTimeTracker: JobSquidGameGuardTriangle
  startingGear: SquidGameTriangle
  icon: "JobIconSquidGameGuardTriangle"
  supervisors: job-supervisors-squidgame-square
  canBeAntag: false
  categories: [ HideSpawnMenu ]
  access:
  - Security
  - Maintenance
  requirements:
    - !type:CharacterSpeciesRequirement
      species:
      - Human
    - !type:CharacterOverallTimeRequirement
      min: 36000 # 10 hours
  special:
  - !type:AddComponentSpecial
    components:
    - type: MindShield
    - type: AntagImmune

# ОХОРОНЕЦЬ КВАДРАТ
- type: job
  id: SquidGameGuardSquare
  name: job-name-squidgame-guard-square
  description: job-description-squidgame-guard-square
  playTimeTracker: JobSquidGameGuardSquare
  startingGear: SquidGameSquare
  icon: "JobIconSquidGameGuardSquare"
  supervisors: job-supervisors-squidgame-uwu
  canBeAntag: false
  categories: [ HideSpawnMenu ]
  access:
  - Security
  - Command
  - Maintenance
  requirements:
    - !type:CharacterSpeciesRequirement
      species:
      - Human
    - !type:CharacterOverallTimeRequirement
      min: 54000 # 15 hours
    - !type:CharacterDepartmentTimeRequirement
      department: Security
      min: 36000 # 10 hours
  special:
  - !type:AddComponentSpecial
    components:
    - type: MindShield
    - type: AntagImmune

# UWU ОХОРОНЕЦЬ (ФРОНТМЕН)
- type: job
  id: SquidGameGuardUwU
  name: job-name-squidgame-guard-uwu
  description: job-description-squidgame-guard-uwu
  playTimeTracker: JobSquidGameGuardUwU
  startingGear: SquidGameUwU
  icon: "JobIconSquidGameGuardUwU"
  supervisors: job-supervisors-centcom
  canBeAntag: false
  categories: [ HideSpawnMenu ]
  weight: 20
  requireAdminNotify: true
  accessGroups:
  - AllAccess
  requirements:
    - !type:CharacterSpeciesRequirement
      species:
      - Human
    - !type:CharacterOverallTimeRequirement
      min: 72000 # 20 hours
    - !type:CharacterDepartmentTimeRequirement
      department: Security
      min: 54000 # 15 hours
    - !type:CharacterDepartmentTimeRequirement
      department: Command
      min: 36000 # 10 hours
    - !type:CharacterWhitelistRequirement
  special:
  - !type:AddComponentSpecial
    components:
    - type: MindShield
    - type: AntagImmune
    - type: CommandStaff
