using Content.Shared.Customization.Systems;
using Content.Shared.Mind;
using Content.Shared.Preferences;
using Content.Shared.Roles;
using JetBrains.Annotations;
using Robust.Shared.Configuration;
using Robust.Shared.Prototypes;
using Robust.Shared.Serialization;
using Robust.Shared.Utility;

namespace Content.Shared.Customization.Systems;

/// <summary>
/// Requirement that removes all traits from the character to ensure equal opportunities.
/// This is used for ivents jobs where all players should have equal chances.
/// </summary>
[UsedImplicitly, Serializable, NetSerializable]
public sealed partial class CharacterRemoveAllTraitsRequirement : CharacterRequirement
{
    public override bool IsValid(
        JobPrototype job,
        HumanoidCharacterProfile profile,
        Dictionary<string, TimeSpan> playTimes,
        bool whitelisted,
        IPrototype prototype,
        IEntityManager entityManager,
        IPrototypeManager prototypeManager,
        IConfigurationManager configManager,
        out string? reason,
        int depth = 0,
        MindComponent? mind = null)
    {
        // This requirement always passes validation
        // The actual trait removal happens in the job special
        reason = null;
        return true;
    }
}
