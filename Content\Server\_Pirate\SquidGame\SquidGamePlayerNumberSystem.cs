using Content.Server.GameTicking.Events;
using Content.Server.Station.Events;
using Content.Shared._Pirate.SquidGame;
using Content.Shared.GameTicking;
using Content.Shared.Roles;
using Content.Shared.Inventory;
using Robust.Shared.Random;

namespace Content.Server._Pirate.SquidGame;

/// <summary>
/// System that manages unique player numbers for Squid Game participants.
/// Assigns numbers from 001 to 456 without repetition.
/// </summary>
public sealed class SquidGamePlayerNumberSystem : EntitySystem
{
    [Dependency] private readonly IRobustRandom _random = default!;
    [Dependency] private readonly InventorySystem _inventorySystem = default!;

    private ISawmill _sawmill = default!;

    /// <summary>
    /// Available player numbers (001-456).
    /// </summary>
    private readonly List<int> _availableNumbers = new();

    /// <summary>
    /// Tracks assigned numbers to prevent duplicates.
    /// </summary>
    private readonly HashSet<int> _assignedNumbers = new();

    public override void Initialize()
    {
        base.Initialize();
        _sawmill = Logger.GetSawmill("pirate.squidgame");

        SubscribeLocalEvent<PlayerSpawnCompleteEvent>(OnPlayerSpawnComplete);
        SubscribeLocalEvent<RoundRestartCleanupEvent>(OnRoundRestart);

        InitializeNumberPool();
    }

    /// <summary>
    /// Initialize the pool of available player numbers (001-456).
    /// </summary>
    private void InitializeNumberPool()
    {
        _availableNumbers.Clear();
        _assignedNumbers.Clear();

        for (int i = 1; i <= 456; i++)
        {
            _availableNumbers.Add(i);
        }

        // Shuffle the numbers for random assignment
        _random.Shuffle(_availableNumbers);

        _sawmill.Info("Initialized Squid Game player number pool with 456 numbers");
    }

    /// <summary>
    /// Handle player spawn completion and assign numbers to Squid Game players.
    /// </summary>
    private void OnPlayerSpawnComplete(PlayerSpawnCompleteEvent ev)
    {
        // Check if the player has a Squid Game job
        if (!IsSquidGamePlayer(ev.Mob))
            return;

        AssignPlayerNumber(ev.Mob);
    }

    /// <summary>
    /// Check if the entity is a Squid Game player.
    /// </summary>
    private bool IsSquidGamePlayer(EntityUid entity)
    {
        if (!TryComp<JobComponent>(entity, out var job))
            return false;

        return job.Prototype?.ID == "SquidGamePlayer";
    }

    /// <summary>
    /// Assign a unique player number to a Squid Game participant.
    /// </summary>
    private void AssignPlayerNumber(EntityUid entity)
    {
        if (_availableNumbers.Count == 0)
        {
            _sawmill.Warning("No more player numbers available for Squid Game!");
            return;
        }

        // Get the next available number
        var playerNumber = _availableNumbers[0];
        _availableNumbers.RemoveAt(0);
        _assignedNumbers.Add(playerNumber);

        // Add the component with the assigned number
        var comp = EnsureComp<SquidGamePlayerNumberComponent>(entity);
        comp.PlayerNumber = playerNumber;
        comp.FormattedNumber = playerNumber.ToString("D3"); // Format as 001, 002, etc.

        Dirty(entity, comp);

        _sawmill.Info($"Assigned player number {comp.FormattedNumber} to entity {entity}");

        // Update entity descriptions that use the player number
        UpdateEntityDescriptions(entity, comp.FormattedNumber);
    }

    /// <summary>
    /// Update entity descriptions to include the player number.
    /// </summary>
    private void UpdateEntityDescriptions(EntityUid entity, string playerNumber)
    {
        // Update ID card description
        if (_inventorySystem.TryGetSlotEntity(entity, "id", out var idEntity))
        {
            if (TryComp<MetaDataComponent>(idEntity, out var idMeta) &&
                idMeta.EntityDescription.Contains("{$number}"))
            {
                idMeta.EntityDescription = idMeta.EntityDescription.Replace("{$number}", playerNumber);
                Dirty(idEntity.Value, idMeta);
                _sawmill.Debug($"Updated ID card description for player {playerNumber}");
            }
        }

        // Update jumpsuit description
        if (_inventorySystem.TryGetSlotEntity(entity, "jumpsuit", out var jumpsuitEntity))
        {
            if (TryComp<MetaDataComponent>(jumpsuitEntity, out var jumpsuitMeta) &&
                jumpsuitMeta.EntityDescription.Contains("{$number}"))
            {
                jumpsuitMeta.EntityDescription = jumpsuitMeta.EntityDescription.Replace("{$number}", playerNumber);
                Dirty(jumpsuitEntity.Value, jumpsuitMeta);
                _sawmill.Debug($"Updated jumpsuit description for player {playerNumber}");
            }
        }
    }

    /// <summary>
    /// Reset the number pool when the round restarts.
    /// </summary>
    private void OnRoundRestart(RoundRestartCleanupEvent ev)
    {
        InitializeNumberPool();
        _sawmill.Info("Reset Squid Game player number pool for new round");
    }

    /// <summary>
    /// Get the player number for an entity, if assigned.
    /// </summary>
    public int? GetPlayerNumber(EntityUid entity)
    {
        if (TryComp<SquidGamePlayerNumberComponent>(entity, out var comp))
            return comp.PlayerNumber;

        return null;
    }

    /// <summary>
    /// Get the formatted player number for an entity, if assigned.
    /// </summary>
    public string? GetFormattedPlayerNumber(EntityUid entity)
    {
        if (TryComp<SquidGamePlayerNumberComponent>(entity, out var comp))
            return comp.FormattedNumber;

        return null;
    }
}
