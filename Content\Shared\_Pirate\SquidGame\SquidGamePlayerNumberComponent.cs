using Robust.Shared.GameStates;

namespace Content.Shared._Pirate.SquidGame;

/// <summary>
/// Component that assigns unique player numbers for Squid Game participants.
/// Player numbers range from 001 to 456 without repetition.
/// </summary>
[RegisterComponent, NetworkedComponent, AutoGenerateComponentState]
public sealed partial class SquidGamePlayerNumberComponent : Component
{
    /// <summary>
    /// The unique player number assigned to this participant (001-456).
    /// </summary>
    [DataField, AutoNetworkedField]
    public int PlayerNumber = -1;

    /// <summary>
    /// The formatted player number as a string (e.g., "001", "456").
    /// </summary>
    [DataField, AutoNetworkedField]
    public string FormattedNumber = string.Empty;
}
